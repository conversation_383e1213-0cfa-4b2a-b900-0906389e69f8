import {
  Avatar,
  List,
  Separator,
  Text,
  UserMenu as UserProfile,
} from "@snap/design-system";
import { getInitials } from "~/helpers";
import { USER_CONSTANTS } from "~/helpers/constants";
import { useUserData } from "~/store/userStore";

interface UserMenuProps {
  userMenuContent: React.ReactNode;
}

export default function UserMenu({ userMenuContent }: UserMenuProps) {
  const userData = useUserData();
  const userImage = userData?.[USER_CONSTANTS.user_data.image as keyof typeof userData] as string;
  const userName = userData?.[USER_CONSTANTS.user_data.name as keyof typeof userData] as string;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;
  const initials = getInitials(userName);

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  const renderUserProfile = () => {
    const profileProps = {
      Profile: (
        <div className="flex items-center gap-3 w-full">
          <Avatar
            size="sm"
            className="size-9"
            src={getValidImageUrl(userImage)}
            fallback={initials || "NA"}
            textAlign="left"
          />
          <List className="space-y-0.5 items-start">
            <span className="text-sm leading-tight">
              {userName || "Sem nome"}
            </span>
            <Separator />
            <Text className="opacity-80">
              {`${userCredits || 0} Consultas`}
            </Text>
          </List>
        </div>
      ),
      Menu: userMenuContent,
    };

    return <UserProfile {...profileProps} menuClassName="py-0 px-0" />;
  };

  return renderUserProfile();
}
