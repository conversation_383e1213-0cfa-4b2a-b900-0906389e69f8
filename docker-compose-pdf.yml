services:
  pdf:
    image: my-pdf-image
    ports:
      - "3002:3002"
    environment:
      NODE_OPTIONS: "--max-old-space-size=1024"
      ENABLE_KAFKA_CONSUMER: ${ENABLE_KAFKA_CONSUMER:-true}
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_URI: ${KAFKA_EXTERNAL_URI}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      SHOULD_USE_KAFKA_CONTAINER: ${SHOULD_USE_KAFKA_CONTAINER:-true}
      KAFKAJS_NO_PARTITIONER_WARNING: "1"
      MINIO_CONTAINER_NAME: ${MINIO_CONTAINER_NAME}
      MINIO_S3_INTERNAL_PORT: ${MINIO_S3_INTERNAL_PORT}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: "0.75"
          memory: "1280M"
        reservations:
          cpus: "0.25"
          memory: 256M
      restart_policy:
        condition: on-failure
    networks:
      - mystack-net
