import { List, ListItem, Menu, MenuItem, Text } from '@snap/design-system'
import { FiTool } from 'react-icons/fi'
import { formatIsoDate, isEmptyObject } from '~/helpers';
import { USER_CONSTANTS } from '~/helpers/constants';
import { useUserData } from '~/store/userStore'
import { ReportCredits } from '~/types/global';

interface UserMenuContentProps {
  menuActions?: MenuItem[] | null;
  showRemainingCredits?: boolean;
  classNameContainer?: string;
}

const UserMenuContent = ({ menuActions = null, showRemainingCredits = false, classNameContainer = "" }: UserMenuContentProps) => {
  const userData = useUserData();
  const userProfile = userData?.[USER_CONSTANTS.user_data.role as keyof typeof userData] as string;
  const creditsValidateUntil = userData?.[USER_CONSTANTS.user_data.next_reset_credits as keyof typeof userData] as string;
  const userReportTypes = userData?.[USER_CONSTANTS.user_data.report_count as keyof typeof userData] as ReportCredits;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;

  const getConsultasRealizadas = () => {
    const userReportsCount = userData?.[USER_CONSTANTS.user_data.report_count as keyof typeof userData] || {};
    const reportSum = Object.values(userReportsCount || {}).reduce((acc, cur) => acc + cur, 0);
    return reportSum;
  };

  return (
    <div>
      {/* tag tipo perfil */}
      <div className="flex items-center gap-2 bg-[#2F3240] p-3 mb-1">
        <FiTool size={16} />
        <Text variant="body-md" className="font-semibold capitalize">{userProfile || "Não Informado"}</Text>
      </div>

      <div className={`flex flex-col gap-4 p-4 ${classNameContainer}`}>
        {/* consultas disponíveis */}
        {
          showRemainingCredits ? (
            <div className="flex items-center gap-2 justify-between">
              <div className="flex flex-col items-start gap-0.5">
                <Text variant="body-lg" className="font-semibold">Consultas disponíveis</Text>
                {creditsValidateUntil ? (
                  <Text variant="body-sm" className="opacity-80">{`Válidas até ${formatIsoDate(creditsValidateUntil, true)}`}</Text>
                ) : null}
              </div>
              <Text variant="title-lg" className="font-semibold">{userCredits || 0}</Text>
            </div>
          ) : null
        }
        <List className="flex flex-col gap-4">
          {/* consultas realizadas */}
          <ListItem className="flex items-center gap-2 bg-accent py-1.5 px-2 justify-between rounded-xs">
            <Text variant="body-md">Consultas realizadas:</Text>
            <Text variant="body-md" className="font-semibold">{getConsultasRealizadas()}</Text>
          </ListItem>
          {/* tipos de relatório */}
          <div className="flex flex-col gap-0.5">
            {userReportTypes &&
              !isEmptyObject(userReportTypes) ? (
              Object.entries(userReportTypes)?.map(([key, value], index) => (
                <ListItem
                  key={index}
                  className="flex items-center gap-2 bg-card py-1.5 px-2 justify-between rounded-xs uppercase"
                >
                  <Text >{key}</Text>
                  <Text variant="body-md" className="font-semibold">{value}</Text>
                </ListItem>
              ))
            ) : (
              <ListItem className="opacity-80">
                Nenhum tipo de relatório permitido.
              </ListItem>
            )}
          </div>
        </List>
        {menuActions ?
          <Menu
            items={menuActions}
            separator
            gap="none"
          /> : null
        }
      </div>

    </div>
  )
}

export default UserMenuContent