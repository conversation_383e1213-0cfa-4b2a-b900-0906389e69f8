import { Kafka, EachMessagePayload } from "kafkajs";
import { GenerateReportsPdf } from "../generateReportsPdf";
import { GenerateReportsHtml } from "../generateReportsHtml";
import { <PERSON><PERSON>er } from "buffer";
import puppeteer, { <PERSON><PERSON><PERSON> } from "puppeteer";
import * as logger from '../../utils/logger';
import { loadTempPdfData, deleteTempPdfData, initializeMinioConnection, storeGeneratedPdf } from '../../config/minio';

const PDF_REQUEST_TOPIC = "pdf-generation-requests";
const PDF_REPLY_TOPIC = "pdf-generation-results";
const PDF_LOG_TOPIC = "pdf-generation-logs";
const GROUP_ID = "pdf-service";

function getKafkaConfiguration() {
  const requiredVars = {
    KAFKA_CONTAINER_NAME: process.env.KAFKA_CONTAINER_NAME,
    KAFKA_INTERNAL_PORT: process.env.KAFKA_INTERNAL_PORT,
    KAFKA_EXTERNAL_URI: process.env.KAFKA_EXTERNAL_URI,
    KAFKA_EXTERNAL_PORT: process.env.KAFKA_EXTERNAL_PORT
  };

  const missingVars = Object.entries(requiredVars)
    .filter(([_, value]) => !value)
    .map(([key, _]) => key);

  if (missingVars.length > 0) {
    logger.error('Missing required Kafka environment variables', { missingVars });
    return null;
  }

  // Determine which broker to use based on SHOULD_USE_KAFKA_CONTAINER
  const shouldUseContainer = process.env.SHOULD_USE_KAFKA_CONTAINER === 'true';

  let kafkaBrokers;
  if (shouldUseContainer) {
    // Use container first, then external as fallback
    kafkaBrokers = [
      `${requiredVars.KAFKA_CONTAINER_NAME}:${requiredVars.KAFKA_INTERNAL_PORT}`,
      `${requiredVars.KAFKA_EXTERNAL_URI}:${requiredVars.KAFKA_EXTERNAL_PORT}`
    ];
  } else {
    // Use external only when explicitly configured not to use container
    kafkaBrokers = [
      `${requiredVars.KAFKA_EXTERNAL_URI}:${requiredVars.KAFKA_EXTERNAL_PORT}`
    ];
    logger.info('Using external Kafka only (container disabled)', {
      externalBroker: `${requiredVars.KAFKA_EXTERNAL_URI}:${requiredVars.KAFKA_EXTERNAL_PORT}`
    });
  }

  return {
    brokers: kafkaBrokers,
    // Fix KafkaJS v2.0.0 partitioner warning
    createPartitioner: require('kafkajs').Partitioners.LegacyPartitioner
  };
}

const kafkaConfig = getKafkaConfiguration();
let kafka: Kafka | null = null;
let consumer: any = null;
let producer: any = null;
let logProducer: any = null;

if (kafkaConfig) {
  kafka = new Kafka({
    brokers: kafkaConfig.brokers,
    // Add connection timeout and retry configuration
    connectionTimeout: 3000,
    requestTimeout: 25000,
    retry: {
      initialRetryTime: 100,
      retries: 8
    }
  });
  consumer = kafka.consumer({ groupId: GROUP_ID });
  producer = kafka.producer({
    createPartitioner: kafkaConfig.createPartitioner
  });
  logProducer = kafka.producer({
    createPartitioner: kafkaConfig.createPartitioner
  });
}

let browser: Browser | null = null;

async function logEvent(event: any) {
  if (!logProducer) {
    logger.warn('Kafka log producer not available, skipping event log', event);
    return;
  }

  try {
    await logProducer.send({
      topic: PDF_LOG_TOPIC,
      messages: [{ value: JSON.stringify(event) }]
    });
  } catch (error) {
    logger.error('Failed to send log event to Kafka', { event, error });
  }
}

export async function startKafkaConsumer() {
  logger.info('Starting Kafka PDF Consumer...');

  if (!kafkaConfig || !kafka || !consumer || !producer || !logProducer) {
    logger.error('Kafka configuration is invalid or missing. Kafka consumer will not start.');
    logger.error('PDF service will continue to work for HTTP requests, but Kafka-based PDF generation will be unavailable.');
    return;
  }

  logger.info('Kafka configuration:', {
    brokers: kafkaConfig.brokers,
    containerName: process.env.KAFKA_CONTAINER_NAME,
    internalPort: process.env.KAFKA_INTERNAL_PORT,
    externalUri: process.env.KAFKA_EXTERNAL_URI,
    externalPort: process.env.KAFKA_EXTERNAL_PORT,
    shouldUseContainer: process.env.SHOULD_USE_KAFKA_CONTAINER
  });

  // Initialize MinIO connection
  logger.info('Initializing MinIO connection...');
  await initializeMinioConnection();
  logger.info('MinIO connection initialized successfully');

  // Initialize browser
  logger.info('Initializing browser for Kafka consumer...');
  browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
  });
  logger.info('Browser initialized successfully for Kafka consumer');

  logger.info('Connecting to Kafka...');
  await consumer.connect();
  await producer.connect();
  await logProducer.connect();
  logger.info('Connected to Kafka successfully');

  await consumer.subscribe({ topic: PDF_REQUEST_TOPIC });
  logger.info('Subscribed to pdf-generation-requests topic');

  logger.info('Kafka PDF Consumer is ready and waiting for messages...');

  await consumer.run({
    eachMessage: async ({ message }: EachMessagePayload) => {
      const kafkaMessage = JSON.parse(message.value!.toString());
      logger.info('Processing PDF generation request', { requestId: kafkaMessage.requestId });
      logEvent({ requestId: kafkaMessage.requestId, status: 'started', timestamp: Date.now(), details: { dataReference: kafkaMessage.dataReference } });

      try {
        if (!browser) {
          throw new Error('Browser not initialized');
        }

        // Validate Kafka message structure
        if (!kafkaMessage.requestId) {
          throw new Error('Missing required field: requestId');
        }
        if (!kafkaMessage.dataReference) {
          throw new Error('Missing required field: dataReference');
        }

        // Load actual PDF data from MinIO
        logger.info('Loading PDF data from MinIO', {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference
        });

        const data = await loadTempPdfData(kafkaMessage.dataReference);
        if (!data) {
          throw new Error(`Failed to load PDF data from MinIO. Reference: ${kafkaMessage.dataReference}`);
        }

        // Delete temp data immediately after successful load
        logger.info('Deleting temp data after successful load', {
          requestId: kafkaMessage.requestId,
          dataReference: kafkaMessage.dataReference
        });
        await deleteTempPdfData(kafkaMessage.dataReference);

        // Validate loaded data structure
        if (!data.sections || !Array.isArray(data.sections)) {
          throw new Error('Missing required field: sections (must be array)');
        }
        if (!data.metadata) {
          throw new Error('Missing required field: metadata');
        }

        // Extract the same structure as HTTP controller
        const { sections, metadata, profile_image } = data;

        logger.info('Generating HTML for PDF request', {
          requestId: kafkaMessage.requestId,
          sectionsCount: sections?.length,
          reportName: metadata?.report_name
        });
        const html = await GenerateReportsHtml({ sections, metadata, profile_image });
        const pdfBuffer = await GenerateReportsPdf({ ...html, browserRef: browser });

        // Generate filename
        const filename = `${metadata.report_name || 'report'}.pdf`.replace(/[^a-zA-Z0-9.-]/g, '_');

        // Store PDF in MinIO instead of sending through Kafka
        const pdfReference = `pdf-${kafkaMessage.requestId}`;
        logger.info('Storing PDF in MinIO', {
          requestId: kafkaMessage.requestId,
          pdfReference: pdfReference,
          pdfSizeMB: (pdfBuffer.length / 1024 / 1024).toFixed(2),
          filename: filename
        });

        await storeGeneratedPdf(pdfReference, Buffer.from(pdfBuffer), filename);

        // Send only reference through Kafka (much smaller message)
        const resultMessage = {
          requestId: kafkaMessage.requestId,
          filename: filename,
          pdfReference: pdfReference,
          pdfSizeBytes: pdfBuffer.length,
          status: 'success',
          metadata: {
            report_type: kafkaMessage.metadata?.report_type,
            user_reports_id: kafkaMessage.metadata?.user_reports_id,
            user_id: kafkaMessage.metadata?.user_id
          }
        };

        logger.info('Result message', resultMessage);

        const messageSize = JSON.stringify(resultMessage).length;
        logger.info('Sending PDF reference to Kafka', {
          requestId: kafkaMessage.requestId,
          messageSize: messageSize,
          pdfReference: pdfReference,
          filename: filename
        });
        await producer.send({
          topic: PDF_REPLY_TOPIC,
          messages: [{
            key: kafkaMessage.requestId,
            value: JSON.stringify(resultMessage)
          }]
        });
        logger.info('PDF reference sent successfully', { requestId: kafkaMessage.requestId });



        logger.info('PDF generation completed successfully', { requestId: kafkaMessage.requestId });
        logEvent({ requestId: kafkaMessage.requestId, status: 'success', timestamp: Date.now(), filename: filename });
      } catch (err: any) {
        logger.error('PDF generation error', {
          requestId: kafkaMessage.requestId,
          error: err.message,
          stack: err.stack
        });
        logEvent({ requestId: kafkaMessage.requestId, status: 'error', timestamp: Date.now(), error: err.message });



        // Send error response back
        await producer.send({
          topic: PDF_REPLY_TOPIC,
          messages: [{
            key: kafkaMessage.requestId,
            value: JSON.stringify({
              requestId: kafkaMessage.requestId,
              error: err.message,
              status: 'error'
            })
          }]
        });
      }
    }
  });
}

export async function shutdownKafkaConsumer() {
  logger.info('Shutting down Kafka consumer gracefully...');

  if (consumer) {
    await consumer.disconnect();
    logger.info('Kafka consumer disconnected');
  }
  if (producer) {
    await producer.disconnect();
    logger.info('Kafka producer disconnected');
  }
  if (logProducer) {
    await logProducer.disconnect();
    logger.info('Kafka log producer disconnected');
  }

  logger.info('Kafka connections closed successfully');
}
