import React, { useState, useEffect } from 'react';
import { Ava<PERSON>, ChamferBox, CustomLabel } from '@snap/design-system';
import { getInitials } from '../../helpers';
import { REPORT_CONSTANTS } from '../../config/constants';
import { isValidUrl } from '../strategies/helpers.strategy';
import { cn } from './utils';
import { useReportMetadata, useReportType, useProfileImage } from '../../context/ReportContext';
import { ReportsCustomLabel } from './ReportsCustomLabel';
import { getAgeFromBirthDate } from '~/helpers';

const ReportProfileHeader: React.FC = () => {
  const metadata = useReportMetadata();
  const reportType = useReportType();
  const image = useProfileImage();
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);
  const profileName = (metadata[REPORT_CONSTANTS.new_report.subject_name] as string) || 'Nome não disponível';
  const initials = getInitials(profileName);
  const ageFromMeta = metadata[REPORT_CONSTANTS.new_report.subject_age] as string | number | undefined;
  const idade = typeof ageFromMeta === 'number' ? ageFromMeta : getAgeFromBirthDate(metadata[REPORT_CONSTANTS.new_report.subject_age] as string);
  const sexo = (metadata[REPORT_CONSTANTS.new_report.subject_sex] as string) || 'N/A';
  const nomeMae = (metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string) || 'N/A';
  //@ts-ignore
  const documentValue = (metadata[REPORT_CONSTANTS.new_report.report_search_args]?.[reportType]) as string || 'N/A';

  const propsMap = {
    cpf: [
      { label: 'IDADE:', value: idade },
      { label: 'SEXO:', value: sexo },
      { label: 'NOME DA MÃE:', value: nomeMae }
    ],
    telefone: [
      { label: 'IDADE:', value: idade },
      { label: 'SEXO:', value: sexo },
      { label: 'NOME DA MÃE:', value: nomeMae }
    ],
    email: [
      { label: 'IDADE:', value: idade },
      { label: 'SEXO:', value: sexo },
      { label: 'NOME DA MÃE:', value: nomeMae }
    ],
    cnpj: [
      { label: 'STATUS NA RECEITA:', value: 'ATIVO' }, // TODO - mockado porque ainda não temos esse dado no metadata
      { label: 'DATA DE FUNDAÇÃO:', value: 'N/A' } // TODO - mockado porque ainda não temos esse dado no metadata
    ]
  } as Record<string, Array<{ label: string; value: any }>>;

  const renderProps = () => (
    (propsMap[reportType] || []).map((prop) => (
      <div key={prop.label}>
        <ReportsCustomLabel label={prop.label} />
        <p>{prop.value}</p>
      </div>
    ))
  );

  useEffect(() => {
    setIsAvatarLoading(true);
    if (!image) {
      setIsAvatarLoading(false);
      return;
    }
    const img = new Image();
    img.src = image;
    img.onload = () => setIsAvatarLoading(false);
    img.onerror = () => setIsAvatarLoading(false);
    const timer = setTimeout(() => setIsAvatarLoading(false), 5000);
    return () => clearTimeout(timer);
  }, [image]);

  return (
    <ChamferBox corner="bottomRight" className="border-none bg-card w-full rounded-xl">
      <div className="flex gap-6">
        <div className="flex-1/8 relative h-51 w-51">
          {isAvatarLoading && (
            <div className="absolute inset-0 z-10 flex items-center justify-center h-full">
              <div className="animate-spin h-20 w-20 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          )}
          <div className={`${isAvatarLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-1000 ease-in-out`}>
            <Avatar
              size="22"
              className={cn(
                "border-8 border-border w-full h-full",
                "[&_img]:h-full [&_img]:w-auto [&_img]:object-cover",
                "[&_span]:h-46 [&_span]:w-46 [&_span]:text-6xl"
              )}

              src={isValidUrl(image || '') ? image : undefined}
              fallback={initials}
              textAlign="left"
            />
          </div>
        </div>


        <div className="flex-2/4 space-y-4">
          <h1 className="text-3xl font-bold">{profileName}</h1>
          <div className="grid grid-cols-2 gap-4">{renderProps()}</div>
        </div>
        <div className="flex-1/4 border-l border-gray-700 pl-4 space-y-4">
          <h2 className="text-lg font-mono">ENTRADAS</h2>
          <div>
            <ReportsCustomLabel label={reportType.toUpperCase()} colorClass="bg-primary" />
            <p>{documentValue}</p>
          </div>
        </div>
      </div>
    </ChamferBox>
  );
};

export default ReportProfileHeader;