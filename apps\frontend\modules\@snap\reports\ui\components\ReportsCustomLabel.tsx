import React, { useState } from "react";
import { Cha<PERSON><PERSON>B<PERSON>, Separator, Text } from '@snap/design-system';
import { Tooltip, TooltipContent, TooltipTrigger } from "./tooltip"

export interface TooltipProps {
  title: string;
  side?: "top" | "bottom" | "left" | "right";
  className?: string;
  children: React.ReactNode;
  icon: React.ReactNode;
}

export interface ReportsCustomLabelProps {
  label: string;
  icon?: React.ReactNode;
  colorClass?: string;
  labelTextClass?: string;
  tooltip?: TooltipProps;
}

export const ReportsCustomLabel: React.FC<ReportsCustomLabelProps> = ({
  label,
  icon = undefined,
  colorClass = 'bg-border',
  tooltip = undefined,
  labelTextClass = '',
}) => {
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);

  const handleTooltipClick = () => {
    setIsTooltipOpen(prev => !prev);
  };

  return (<div className="flex items-center gap-2 mb-0">
    {icon && icon}
    <div className={`h-2.5 w-2.5 ${colorClass === 'bg-border' ? 'bg-secondary' : colorClass}`} /> {/* TODO - remover gambi para alterar todos bg-border em secondary */}
    <Text className={`font-mono text-sm text-secondary ${colorClass === 'bg-accent' || colorClass === "bg-primary" ? 'text-accent' : labelTextClass}`}>
      {label}
    </Text>
    {tooltip && (
      <Tooltip
        open={isTooltipOpen}
        onOpenChange={setIsTooltipOpen}
      >
        <TooltipTrigger
          className="cursor-help"
          asChild
          onClick={handleTooltipClick}
        >
          <span
            className="
              opacity-0
              transition-all duration-150
              group-hover:opacity-100
              hover:scale-110
              cursor-help
            "
          >
            {tooltip.icon}
          </span>
        </TooltipTrigger>
        <TooltipContent side={tooltip.side || "right"} className="!p-0 !bg-transparent !shadow-none !border-none">
          <ChamferBox corner="topLeft" className="rounded-md px-1 pt-1 bg-card">
            {/* overlay para escurecer a cor de fundo */}
            <div className="absolute inset-0 bg-black/30 rounded-md pointer-events-none" />
            <div className="relative z-10">
              {tooltip.children}
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    )}
  </div>)
}

