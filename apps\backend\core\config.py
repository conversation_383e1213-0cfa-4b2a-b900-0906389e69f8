import os
import logging
from pathlib import Path
from pydantic_settings import BaseSettings
from minio import Minio

logger = logging.getLogger(__name__)

def read_docker_secret(path: str) -> str:
    try:
        secret = Path(path).read_text().strip()
        logger.info(f"Successfully read secret from {path}")
        return secret
    except Exception as e:
        logger.error("Could not read secret from %s: %s", path, e)
        return ""

class Settings(BaseSettings):
    DB_HOST: str = os.getenv("DB_HOST")
    DB_NAME: str = os.getenv("DB_NAME")
    DB_USER: str = os.getenv("DB_USER")
    DB_PASS: str = os.getenv("DB_PASS")

    CLIENT_ID_KEYCLOAK: str = os.getenv("CLIENT_ID_KEYCLOAK")
    CLIENT_SECRET_KEYCLOAK: str = ""  # Will assign after init
    SNAP_API_CLIENT_SECRET: str = ""
    KEYCLOAK_VERIFY_SSL: bool = os.getenv("KEYCLOAK_VERIFY_SSL", True)
    CAPTCHA_KEY: str = ""   
    REDIRECT_URI_KEYCLOAK: str = os.getenv("REDIRECT_URI_KEYCLOAK")

    KEYCLOAK_URL: str = os.getenv("KEYCLOAK_URL")
    REALM_NAME: str = os.getenv("REALM_NAME")
    FRONTEND_REDIRECT_URL: str = os.getenv("FRONTEND_REDIRECT_URL")
    BASE_API_URL: str = os.getenv("BASE_API_URL")

    MINIO_ROOT_USER: str = os.getenv("MINIO_ROOT_USER")
    MINIO_ROOT_PASSWORD: str = os.getenv("MINIO_ROOT_PASSWORD")
    MINIO_CONTAINER_NAME: str = os.getenv("MINIO_CONTAINER_NAME")
    MINIO_S3_INTERNAL_PORT: str = os.getenv("MINIO_S3_INTERNAL_PORT")
        
    KAFKA_CONTAINER_NAME: str = os.getenv("KAFKA_CONTAINER_NAME")
    KAFKA_EXTERNAL_URI: str = os.getenv("KAFKA_EXTERNAL_URI")
    KAFKA_INTERNAL_PORT: str = os.getenv("KAFKA_INTERNAL_PORT")
    KAFKA_EXTERNAL_PORT: str = os.getenv("KAFKA_EXTERNAL_PORT")
    SHOULD_USE_KAFKA_CONTAINER: bool = os.getenv("SHOULD_USE_KAFKA_CONTAINER", "true").lower() == "true"

    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "DEBUG") 

    @property
    def JWKS_URL(self):
        return f"{self.KEYCLOAK_URL}/realms/{self.REALM_NAME}/protocol/openid-connect/certs"

    @property
    def REFRESH_URL(self):
        return f"{self.KEYCLOAK_URL}/realms/{self.REALM_NAME}/protocol/openid-connect/token"

    @property
    def DATABASE_URL(self):
        logger.info("Connecting to database at %s:%s/%s", settings.DB_HOST, 5432, settings.DB_NAME)
        return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASS}@{self.DB_HOST}:5432/{self.DB_NAME}"
    
    model_config = {"env_file": ".env"}

# Instantiate settings
settings = Settings()

# Read secrets after instantiation
settings.CLIENT_SECRET_KEYCLOAK = read_docker_secret("/run/secrets/client_secret_keycloak")
settings.SNAP_API_CLIENT_SECRET = read_docker_secret("/run/secrets/snap_api_client_secret")
settings.CAPTCHA_KEY = read_docker_secret("/run/secrets/captcha_key")

# Initialize MinIO client safely
try:
    minio_client = Minio(
        "%s:%s" % (settings.MINIO_CONTAINER_NAME, settings.MINIO_S3_INTERNAL_PORT),
        access_key=settings.MINIO_ROOT_USER,
        secret_key=settings.MINIO_ROOT_PASSWORD,
        secure=False  # Set True if using HTTPS
    )
    logger.info("Successfully initialized MinIO client for %s:%s", settings.MINIO_CONTAINER_NAME, settings.MINIO_S3_INTERNAL_PORT)
except Exception as e:
    logger.error("Failed to initialize MinIO client: %s", e)
    raise

